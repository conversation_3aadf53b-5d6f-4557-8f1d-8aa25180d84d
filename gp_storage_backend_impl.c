/*
 * GP Storage Backend Implementation for Trusty TEE
 * 
 * This file implements the GP storage backend interface that adapts
 * Trusty storage services for GP TEE Internal Core API compliance.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <trusty_std.h>
#include <interface/storage/storage.h>
#include <lib/storage/storage.h>

#include "gp_storage_backend.h"
#include "gp_tee_internal_api.h"

/* 全局存储后端实例 */
static struct gp_storage_backend g_storage_backends[3];
static bool g_backends_initialized = false;
static mutex_t g_backend_lock = MUTEX_INITIAL_VALUE(g_backend_lock);

/* 前向声明 */
static int gp_storage_open_impl(struct gp_storage_backend *backend, 
                               const char *path, uint32_t flags, file_handle_t *fh);
static int gp_storage_create_impl(struct gp_storage_backend *backend, 
                                 const char *path, uint32_t flags,
                                 const void *data, size_t size, file_handle_t *fh);
static int gp_storage_close_impl(struct gp_storage_backend *backend, file_handle_t fh);
static int gp_storage_read_impl(struct gp_storage_backend *backend, file_handle_t fh, 
                               storage_off_t offset, void *buf, size_t size, size_t *bytes_read);
static int gp_storage_write_impl(struct gp_storage_backend *backend, file_handle_t fh, 
                                storage_off_t offset, const void *buf, size_t size);
static int gp_storage_truncate_impl(struct gp_storage_backend *backend, 
                                   file_handle_t fh, storage_off_t size);
static int gp_storage_remove_impl(struct gp_storage_backend *backend, const char *path);
static int gp_storage_rename_impl(struct gp_storage_backend *backend, 
                                 const char *old_path, const char *new_path);
static int gp_storage_get_size_impl(struct gp_storage_backend *backend, 
                                   file_handle_t fh, storage_off_t *size);
static int gp_storage_exists_impl(struct gp_storage_backend *backend, 
                                 const char *path, bool *exists);
static int gp_storage_list_begin_impl(struct gp_storage_backend *backend, 
                                     const char *prefix, void **iter_handle);
static int gp_storage_list_next_impl(struct gp_storage_backend *backend, void *iter_handle, 
                                    char *name, size_t name_size, bool *has_more);
static int gp_storage_list_end_impl(struct gp_storage_backend *backend, void *iter_handle);

/**
 * 获取存储类型对应的端口名
 */
static const char *gp_get_storage_port(uint32_t storage_type) {
    switch (storage_type) {
        case TEE_STORAGE_PRIVATE:
            return STORAGE_CLIENT_TD_PORT;
        case TEE_STORAGE_PERSO:
            return STORAGE_CLIENT_TDP_PORT;
        case TEE_STORAGE_PROTECTED:
            return STORAGE_CLIENT_TP_PORT;
        default:
            return NULL;
    }
}

/**
 * 初始化存储后端
 */
int gp_storage_backend_init(struct gp_storage_backend *backend, uint32_t storage_type) {
    const char *port_name;
    int ret;
    
    if (!backend)
        return -EINVAL;
    
    port_name = gp_get_storage_port(storage_type);
    if (!port_name)
        return -EINVAL;
    
    /* 打开存储会话 */
    ret = storage_open_session(&backend->session, port_name);
    if (ret != NO_ERROR)
        return ret;
    
    backend->port_name = port_name;
    backend->session_active = true;
    
    /* 设置函数指针 */
    backend->open = gp_storage_open_impl;
    backend->create = gp_storage_create_impl;
    backend->close = gp_storage_close_impl;
    backend->read = gp_storage_read_impl;
    backend->write = gp_storage_write_impl;
    backend->truncate = gp_storage_truncate_impl;
    backend->remove = gp_storage_remove_impl;
    backend->rename = gp_storage_rename_impl;
    backend->get_size = gp_storage_get_size_impl;
    backend->exists = gp_storage_exists_impl;
    backend->list_begin = gp_storage_list_begin_impl;
    backend->list_next = gp_storage_list_next_impl;
    backend->list_end = gp_storage_list_end_impl;
    
    return NO_ERROR;
}

/**
 * 销毁存储后端
 */
void gp_storage_backend_destroy(struct gp_storage_backend *backend) {
    if (!backend)
        return;
    
    if (backend->session_active) {
        storage_close_session(backend->session);
        backend->session_active = false;
    }
}

/**
 * 获取存储后端实例
 */
struct gp_storage_backend *gp_get_storage_backend(uint32_t storage_type) {
    struct gp_storage_backend *backend;
    int backend_idx;
    int ret;
    
    /* 确定后端索引 */
    switch (storage_type) {
        case TEE_STORAGE_PRIVATE:
            backend_idx = 0;
            break;
        case TEE_STORAGE_PERSO:
            backend_idx = 1;
            break;
        case TEE_STORAGE_PROTECTED:
            backend_idx = 2;
            break;
        default:
            return NULL;
    }
    
    mutex_acquire(&g_backend_lock);
    
    /* 延迟初始化 */
    if (!g_backends_initialized) {
        memset(g_storage_backends, 0, sizeof(g_storage_backends));
        g_backends_initialized = true;
    }
    
    backend = &g_storage_backends[backend_idx];
    
    /* 如果后端未初始化，则初始化它 */
    if (!backend->session_active) {
        ret = gp_storage_backend_init(backend, storage_type);
        if (ret != NO_ERROR) {
            mutex_release(&g_backend_lock);
            return NULL;
        }
    }
    
    mutex_release(&g_backend_lock);
    return backend;
}

/**
 * Trusty存储错误码到GP错误码映射
 */
TEE_Result gp_convert_storage_error(int storage_err) {
    switch (storage_err) {
        case NO_ERROR:
            return TEE_SUCCESS;
        case ERR_NOT_FOUND:
            return TEE_ERROR_ITEM_NOT_FOUND;
        case ERR_ACCESS_DENIED:
            return TEE_ERROR_ACCESS_DENIED;
        case ERR_ALREADY_EXISTS:
            return TEE_ERROR_ACCESS_CONFLICT;
        case ERR_NO_MEMORY:
            return TEE_ERROR_OUT_OF_MEMORY;
        case ERR_IO:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        case ERR_INVALID_ARGS:
            return TEE_ERROR_BAD_PARAMETERS;
        case ERR_NOT_ALLOWED:
            return TEE_ERROR_ACCESS_DENIED;
        case ERR_BUSY:
            return TEE_ERROR_BUSY;
        case ERR_GENERIC:
            return TEE_ERROR_GENERIC;
        case STORAGE_ERR_NOT_VALID:
            return TEE_ERROR_BAD_PARAMETERS;
        case STORAGE_ERR_UNIMPLEMENTED:
            return TEE_ERROR_NOT_SUPPORTED;
        case STORAGE_ERR_ACCESS:
            return TEE_ERROR_ACCESS_DENIED;
        case STORAGE_ERR_EXIST:
            return TEE_ERROR_ACCESS_CONFLICT;
        case STORAGE_ERR_TRANSACT:
            return TEE_ERROR_BAD_STATE;
        case STORAGE_ERR_SYNC_FAILURE:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        case STORAGE_ERR_CORRUPTED:
            return TEE_ERROR_CORRUPT_OBJECT;
        case STORAGE_ERR_FS_REPAIRED:
            return TEE_ERROR_STORAGE_NOT_AVAILABLE;
        default:
            return TEE_ERROR_GENERIC;
    }
}

/**
 * 构建TA隔离的存储路径
 */
int gp_build_ta_storage_path(const struct uuid *ta_uuid, 
                            const void *obj_id, uint32_t obj_id_len,
                            uint32_t storage_type, char *path, size_t path_size) {
    char uuid_str[37];
    char obj_id_hex[513];  /* 最大256字节对象ID的十六进制表示 */
    const char *storage_prefix;
    
    if (!ta_uuid || !obj_id || !path || obj_id_len > 256)
        return -EINVAL;
    
    /* 根据存储类型选择前缀 */
    switch (storage_type) {
        case TEE_STORAGE_PRIVATE:
            storage_prefix = "private";
            break;
        case TEE_STORAGE_PERSO:
            storage_prefix = "perso";
            break;
        case TEE_STORAGE_PROTECTED:
            storage_prefix = "protected";
            break;
        default:
            return -EINVAL;
    }
    
    /* 格式化UUID字符串 */
    snprintf(uuid_str, sizeof(uuid_str), 
             "%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x",
             ta_uuid->time_low, ta_uuid->time_mid, ta_uuid->time_hi_and_version,
             ta_uuid->clock_seq_and_node[0], ta_uuid->clock_seq_and_node[1],
             ta_uuid->clock_seq_and_node[2], ta_uuid->clock_seq_and_node[3],
             ta_uuid->clock_seq_and_node[4], ta_uuid->clock_seq_and_node[5],
             ta_uuid->clock_seq_and_node[6], ta_uuid->clock_seq_and_node[7]);
    
    /* 转换对象ID为十六进制字符串 */
    const uint8_t *id_bytes = (const uint8_t *)obj_id;
    for (uint32_t i = 0; i < obj_id_len; i++) {
        snprintf(&obj_id_hex[i * 2], 3, "%02x", id_bytes[i]);
    }
    obj_id_hex[obj_id_len * 2] = '\0';
    
    /* 构建完整路径：storage_type/ta_uuid/obj_id_hex */
    int ret = snprintf(path, path_size, "%s/%s/%s", 
                      storage_prefix, uuid_str, obj_id_hex);
    
    if (ret >= (int)path_size)
        return -ENAMETOOLONG;
    
    return NO_ERROR;
}

/* ============================================================================
 * 存储操作实现函数
 * ============================================================================ */

/**
 * 打开文件实现
 */
static int gp_storage_open_impl(struct gp_storage_backend *backend, 
                               const char *path, uint32_t flags, file_handle_t *fh) {
    if (!backend || !path || !fh || !backend->session_active)
        return -EINVAL;
    
    return storage_open_file(backend->session, fh, path, flags, 0);
}

/**
 * 创建文件实现
 */
static int gp_storage_create_impl(struct gp_storage_backend *backend, 
                                 const char *path, uint32_t flags,
                                 const void *data, size_t size, file_handle_t *fh) {
    int ret;
    
    if (!backend || !path || !fh || !backend->session_active)
        return -EINVAL;
    
    /* 创建文件 */
    ret = storage_open_file(backend->session, fh, path, 
                           flags | STORAGE_FILE_OPEN_CREATE, 0);
    if (ret != NO_ERROR)
        return ret;
    
    /* 写入初始数据 */
    if (data && size > 0) {
        ssize_t written = storage_write(*fh, 0, data, size, 0);
        if (written < 0) {
            storage_close_file(*fh);
            return (int)written;
        }
        if ((size_t)written != size) {
            storage_close_file(*fh);
            return -EIO;
        }
    }
    
    return NO_ERROR;
}

/**
 * 关闭文件实现
 */
static int gp_storage_close_impl(struct gp_storage_backend *backend, file_handle_t fh) {
    if (!backend || !backend->session_active)
        return -EINVAL;

    storage_close_file(fh);
    return NO_ERROR;
}

/**
 * 读取文件实现
 */
static int gp_storage_read_impl(struct gp_storage_backend *backend, file_handle_t fh,
                               storage_off_t offset, void *buf, size_t size, size_t *bytes_read) {
    ssize_t ret;

    if (!backend || !buf || !bytes_read || !backend->session_active)
        return -EINVAL;

    ret = storage_read(fh, offset, buf, size);
    if (ret < 0)
        return (int)ret;

    *bytes_read = (size_t)ret;
    return NO_ERROR;
}

/**
 * 写入文件实现
 */
static int gp_storage_write_impl(struct gp_storage_backend *backend, file_handle_t fh,
                                storage_off_t offset, const void *buf, size_t size) {
    ssize_t ret;

    if (!backend || !buf || !backend->session_active)
        return -EINVAL;

    ret = storage_write(fh, offset, buf, size, 0);
    if (ret < 0)
        return (int)ret;

    if ((size_t)ret != size)
        return -EIO;

    return NO_ERROR;
}

/**
 * 截断文件实现
 */
static int gp_storage_truncate_impl(struct gp_storage_backend *backend,
                                   file_handle_t fh, storage_off_t size) {
    if (!backend || !backend->session_active)
        return -EINVAL;

    return storage_set_file_size(fh, size, 0);
}

/**
 * 删除文件实现
 */
static int gp_storage_remove_impl(struct gp_storage_backend *backend, const char *path) {
    if (!backend || !path || !backend->session_active)
        return -EINVAL;

    return storage_delete_file(backend->session, path, 0);
}

/**
 * 重命名文件实现
 */
static int gp_storage_rename_impl(struct gp_storage_backend *backend,
                                 const char *old_path, const char *new_path) {
    if (!backend || !old_path || !new_path || !backend->session_active)
        return -EINVAL;

    return storage_move_file(backend->session, old_path, new_path,
                           STORAGE_FILE_MOVE_CREATE);
}

/**
 * 获取文件大小实现
 */
static int gp_storage_get_size_impl(struct gp_storage_backend *backend,
                                   file_handle_t fh, storage_off_t *size) {
    if (!backend || !size || !backend->session_active)
        return -EINVAL;

    return storage_get_file_size(fh, size);
}

/**
 * 检查文件存在实现
 */
static int gp_storage_exists_impl(struct gp_storage_backend *backend,
                                 const char *path, bool *exists) {
    file_handle_t fh;
    int ret;

    if (!backend || !path || !exists || !backend->session_active)
        return -EINVAL;

    /* 尝试打开文件来检查存在性 */
    ret = storage_open_file(backend->session, &fh, path,
                           STORAGE_FILE_OPEN_READ, 0);
    if (ret == NO_ERROR) {
        storage_close_file(fh);
        *exists = true;
        return NO_ERROR;
    } else if (ret == ERR_NOT_FOUND) {
        *exists = false;
        return NO_ERROR;
    } else {
        return ret;
    }
}

/**
 * 开始目录枚举实现
 */
static int gp_storage_list_begin_impl(struct gp_storage_backend *backend,
                                     const char *prefix, void **iter_handle) {
    struct gp_dir_enum_state *enum_state;
    int ret;

    if (!backend || !prefix || !iter_handle || !backend->session_active)
        return -EINVAL;

    /* 分配枚举状态 */
    enum_state = calloc(1, sizeof(*enum_state));
    if (!enum_state)
        return -ENOMEM;

    /* 初始化枚举状态 */
    enum_state->session = backend->session;
    strncpy(enum_state->prefix, prefix, sizeof(enum_state->prefix) - 1);
    enum_state->active = true;

    /* 开始目录枚举 */
    ret = storage_open_dir(backend->session, prefix, &enum_state->state);
    if (ret != NO_ERROR) {
        free(enum_state);
        return ret;
    }

    *iter_handle = enum_state;
    return NO_ERROR;
}

/**
 * 获取下一个目录项实现
 */
static int gp_storage_list_next_impl(struct gp_storage_backend *backend, void *iter_handle,
                                    char *name, size_t name_size, bool *has_more) {
    struct gp_dir_enum_state *enum_state = (struct gp_dir_enum_state *)iter_handle;
    uint8_t flags;
    int ret;

    if (!backend || !enum_state || !name || !has_more || !backend->session_active)
        return -EINVAL;

    if (!enum_state->active) {
        *has_more = false;
        return NO_ERROR;
    }

    /* 读取下一个目录项 */
    ret = storage_read_dir(enum_state->session, &enum_state->state,
                          &flags, name, name_size);
    if (ret == NO_ERROR) {
        *has_more = true;
    } else if (ret == ERR_NOT_FOUND) {
        *has_more = false;
        ret = NO_ERROR;
    } else {
        *has_more = false;
        enum_state->active = false;
    }

    return ret;
}

/**
 * 结束目录枚举实现
 */
static int gp_storage_list_end_impl(struct gp_storage_backend *backend, void *iter_handle) {
    struct gp_dir_enum_state *enum_state = (struct gp_dir_enum_state *)iter_handle;

    if (!backend || !enum_state)
        return -EINVAL;

    /* 清理枚举状态 */
    if (enum_state->active) {
        storage_close_dir(enum_state->session, &enum_state->state);
        enum_state->active = false;
    }

    free(enum_state);
    return NO_ERROR;
}

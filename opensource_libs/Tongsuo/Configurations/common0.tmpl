{- # -*- Mode: perl -*-

 # Commonly used list of generated files
 # The reason for the complexity is that the build.info files provide
 # GENERATE rules for *all* platforms without discrimination, while the
 # build files only want those for a particular build.  Therefore, we
 # need to extrapolate exactly what we need to generate.  The way to do
 # that is to extract all possible source files from diverse tables and
 # filter out all that are not generated
 my %generatables =
     map { $_ => 1 }
     ( # The sources of stuff may be generated
         ( map { @{$unified_info{sources}->{$_}} }
               keys %{$unified_info{sources}} ),
         $disabled{shared}
             ? ()
             : ( map { @{$unified_info{shared_sources}->{$_}} }
                 keys %{$unified_info{shared_sources}} ),
         # Things we explicitly depend on are usually generated
         ( map { $_ eq "" ? () : @{$unified_info{depends}->{$_}} }
               keys %{$unified_info{depends}} ));
 our @generated =
     sort ( ( grep { defined $unified_info{generate}->{$_} }
              sort keys %generatables ),
            # Scripts are assumed to be generated, so add them too
            ( grep { defined $unified_info{sources}->{$_} }
              @{$unified_info{scripts}} ) );

 # Avoid strange output
 "";
-}

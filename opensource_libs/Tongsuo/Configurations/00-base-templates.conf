# -*- Mode: perl -*-
my %targets=(
    DEFAULTS => {
	template	=> 1,

	cflags		=> "",
	cppflags	=> "",
	lflags		=> "",
	defines		=> [],
	includes	=> [],
	lib_cflags	=> "",
	lib_cppflags	=> "",
	lib_defines	=> [],
	thread_scheme	=> "(unknown)", # Assume we don't know
	thread_defines	=> [],

	unistd		=> "<unistd.h>",
	shared_target	=> "",
	shared_cflag	=> "",
	shared_defines	=> [],
	shared_ldflag	=> "",
	shared_rcflag	=> "",

	#### Defaults for the benefit of the config targets who don't inherit
	#### a BASE and assume Unix defaults
	#### THESE WILL DISAPPEAR IN OpenSSL 1.2
	build_scheme	=> [ "unified", "unix" ],
	build_file	=> "Makefile",

	AR		=> "(unused)",
	ARFLAGS		=> "(unused)",
	CC		=> "cc",
	HASHBANGPERL	=> "/usr/bin/env perl",
	RANLIB		=> sub { which("$config{cross_compile_prefix}ranlib")
                                     ? "ranlib" : "" },
	RC		=> "windres",

	#### THESE WILL BE ENABLED IN OpenSSL 1.2
	#HASHBANGPERL	=> "PERL", # Only Unix actually cares
    },

    BASE_common => {
	template	=> 1,

	enable		=> [],
	disable		=> [],

	defines		=>
	    sub {
                my @defs = ( 'OPENSSL_BUILDING_OPENSSL' );
                push @defs, "ZLIB" unless $disabled{zlib};
                push @defs, "ZLIB_SHARED" unless $disabled{"zlib-dynamic"};

                push @defs, "SDF_LIB" unless $disabled{"sdf-lib"};
                push @defs, "SDF_LIB_SHARED" unless $disabled{"sdf-lib-dynamic"};
                return [ @defs ];
            },
        includes        =>
            sub {
                my @incs = ();
                push @incs, $withargs{zlib_include}
                    if !$disabled{zlib} && $withargs{zlib_include};

                push @incs, $withargs{sdf_include}
                    if !$disabled{sdf_lib} && $withargs{sdf_include};
                return [ @incs ];
            },
    },

    BASE_unix => {
        inherit_from    => [ "BASE_common" ],
        template        => 1,

        AR              => "ar",
        ARFLAGS         => "qc",
        CC              => "cc",
        lflags          =>
            sub { $withargs{zlib_lib} ? "-L".$withargs{zlib_lib} : () },
        ex_libs         =>
            sub {
                my @libs = ();
                unless ($disabled{zlib}) {
                    if (defined($disabled{"zlib-dynamic"})) {
                        push @libs, "-lz";
                    }
                }
                # providers/smtc/self_test_rand.c depends libm
                push @libs, '-lm' unless $disabled{"smtc"};
                return join(' ', @libs);
            },

        HASHBANGPERL    => "/usr/bin/env perl", # Only Unix actually cares
        RANLIB          => sub { which("$config{cross_compile_prefix}ranlib")
                                     ? "ranlib" : "" },
        RC              => "windres",

        build_scheme    => [ "unified", "unix" ],
        build_file      => "Makefile",

        perl_platform   => 'Unix',
    },

    BASE_Windows => {
        inherit_from    => [ "BASE_common" ],
        template        => 1,

        lib_defines      =>
            sub {
                my @defs = ();
                unless ($disabled{"zlib-dynamic"}) {
                    my $zlib = $withargs{zlib_lib} // "ZLIB1";
                    push @defs, 'LIBZ=' . (quotify("perl", $zlib))[0];
                }
                return [ @defs ];
            },
        ex_libs         =>
            sub {
                my @libs = ();
                unless ($disabled{zlib}) {
                    if (defined($disabled{"zlib-dynamic"})) {
                        push @libs, $withargs{zlib_lib} // "ZLIB1";
                    }
                }
                # providers/smtc/self_test_rand.c depends libm
                push @libs, '-lm' unless $disabled{"smtc"};
                return join(' ', @libs);
            },

        MT              => "mt",
        MTFLAGS         => "-nologo",
        mtinflag        => "-manifest ",
        mtoutflag       => "-outputresource:",

        build_file      => "makefile",
        build_scheme    => [ "unified", "windows" ],

        perl_platform   => 'Windows',
    },
);

# Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

name: Windows GitHub CI

on: [pull_request, push]

jobs:
  shared:
    # Run a job for each of the specified target architectures:
    strategy:
      matrix:
        os:
          - windows-2019
          - windows-2022
        platform:
          - arch: win64
            config: VC-WIN64A enable-fips
          - arch: win32
            config: VC-WIN32 --strict-warnings no-fips
    runs-on: ${{matrix.os}}
    steps:
    - uses: actions/checkout@v2
    - uses: ilammy/msvc-dev-cmd@v1
      with:
        arch: ${{ matrix.platform.arch }}
    - uses: ilammy/setup-nasm@v1
      with:
        platform: ${{ matrix.platform.arch }}
    - uses: shogo82148/actions-setup-perl@v1
    - name: prepare the build directory
      run: mkdir _build
    - name: config
      working-directory: _build
      run: |
        perl ..\Configure --banner=Configured no-makedepend ${{ matrix.platform.config }}
        perl configdata.pm --dump
    - name: build
      working-directory: _build
      run: nmake /S
    - name: test
      working-directory: _build
      run: nmake test VERBOSE_FAILURE=yes TESTS=-test_fuzz* HARNESS_JOBS=4
    - name: install
      # Run on 64 bit only as 32 bit is slow enough already
      if: $${{ matrix.platform.arch == 'win64' }}
      run: |
        mkdir _dest
        nmake install DESTDIR=_dest
      working-directory: _build
  plain:
    strategy:
      matrix:
        os:
          - windows-2019
          - windows-2022
    runs-on: ${{matrix.os}}
    steps:
    - uses: actions/checkout@v2
    - uses: ilammy/msvc-dev-cmd@v1
    - uses: shogo82148/actions-setup-perl@v1
    - name: prepare the build directory
      run: mkdir _build
    - name: config
      working-directory: _build
      run: |
        perl ..\Configure --banner=Configured no-makedepend no-shared no-fips VC-WIN64A-masm
        perl configdata.pm --dump
    - name: build
      working-directory: _build
      run: nmake /S
    - name: test
      working-directory: _build
      run: nmake test VERBOSE_FAILURE=yes HARNESS_JOBS=4
  minimal:
    strategy:
      matrix:
        os:
          - windows-2019
          - windows-2022
    runs-on: ${{matrix.os}}
    steps:
    - uses: actions/checkout@v2
    - uses: ilammy/msvc-dev-cmd@v1
    - uses: shogo82148/actions-setup-perl@v1
    - name: prepare the build directory
      run: mkdir _build
    - name: config
      working-directory: _build
      run: |
        perl ..\Configure --banner=Configured no-makedepend no-bulk no-deprecated no-fips no-asm -DOPENSSL_SMALL_FOOTPRINT VC-WIN64A
        perl configdata.pm --dump
    - name: build
      working-directory: _build
      run: nmake # verbose, so no /S here
    - name: test
      working-directory: _build
      run: nmake test VERBOSE_FAILURE=yes TESTS=-test_fuzz* HARNESS_JOBS=4
